# CMake
CMakeLists.txt.user
CMakeCache.txt
CMakeFiles
CMakeScripts
Testing
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake

# Python
__pycache__/
*.py[cod]
*$py.class

# Ninja
.ninja_deps
.ninja_log

# C Prerequisites
*.d

# C Object files
*.o
*.ko
*.obj
*.elf

# C Linker output
*.ilk
*.exp

# C Precompiled Headers
*.gch
*.pch

# C Libraries
*.lib
*.a
*.la
*.lo

# C Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# C Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# C Debug files
*.dSYM/
*.su
*.idb
*.pdb

# C Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

# Editor save files
*~

# Vim <PERSON>wap
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Vim Session
Session.vim

# Vim Temporary
.netrwhist
*~
# Vim Auto-generated tag files
tags
# Vim Persistent undo
[._]*.un~

# VScode config
.vscode

# Build directories
install/*
build/*

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# Visual Studio 2015/2017 cache/options directory
.vs/

# Visual Studio 2017 auto generated files
Generated\ Files/

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Visual C++ cache files
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# Visual Studio Trace Files
*.e2e

# gcov profiling files
*.gcda
*.gcno

# ClamAV build system generated files
ClamAV.VC.db
libclamav.pc
clamav-config.h
platform.h
/target.h
libclamav/c++/FileCheck
libclamav/c++/count
libclamav/c++/clamavcxx-config.h
libclamav/c++/llc
libclamav/c++/lli
libclamav/c++/llvm-as
libclamav/c++/llvm-dis
libclamav/c++/llvm/Makefile.config
libclamav/c++/llvm/Debug/
libclamav/c++/llvm/Release
libclamav/c++/llvm/docs/doxygen.cfg
libclamav/c++/llvm/include/llvm/Config/*.def
libclamav/c++/llvm/include/llvm/Config/config.h
libclamav/c++/llvm/include/llvm/System/DataTypes.h
libclamav/c++/llvm/llvm.spec
libclamav/c++/llvm/mklib
libclamav/c++/llvm/test/*/Output
libclamav/c++/llvm/test/*/.dir
libclamav/c++/llvm/test/*/*/Output
libclamav/c++/llvm/test/*/*/*/Output
libclamav/c++/llvm/test/lit.site.cfg
libclamav/c++/llvm/test/Unit/lit.site.cfg
libclamav/c++/llvm/test/site.exp
libclamav/c++/llvm/tools/llvm-config/llvm-config.in
libclamav/c++/llvm/utils/lit/*.pyc
libclamav/c++/llvmunittest_ADT
libclamav/c++/llvmunittest_ExecutionEngine
libclamav/c++/llvmunittest_JIT
libclamav/c++/llvmunittest_Support
libclamav/c++/llvmunittest_VMCore
libclamav/c++/not
libclamav/c++/tblgen
libclamav/c++/llvm/tools/llvmc/plugins/Base/Base.td
win32/res/common.rc

# Generated by Cargo
# will have compiled files and executables
debug/
target/

# These are backup files generated by rustfmt
**/*.rs.bk
