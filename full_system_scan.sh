#!/bin/bash
# ClamAV Full System Scan Script

# Load ClamAV environment
export PATH="$HOME/clamav/bin:$PATH"
export DYLD_LIBRARY_PATH="$HOME/clamav/lib:$DYLD_LIBRARY_PATH"

# Create scan log directory
SCAN_LOG_DIR="$HOME/clamav/scan_logs"
mkdir -p "$SCAN_LOG_DIR"

# Generate timestamp for log files
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SCAN_LOG="$SCAN_LOG_DIR/full_scan_$TIMESTAMP.log"
INFECTED_LOG="$SCAN_LOG_DIR/infected_files_$TIMESTAMP.log"

echo "=== ClamAV Full System Scan Started ===" | tee "$SCAN_LOG"
echo "Scan started at: $(date)" | tee -a "$SCAN_LOG"
echo "Log file: $SCAN_LOG" | tee -a "$SCAN_LOG"
echo "Infected files log: $INFECTED_LOG" | tee -a "$SCAN_LOG"
echo "" | tee -a "$SCAN_LOG"

# Scan parameters
SCAN_OPTIONS="--recursive --verbose --infected --log=$SCAN_LOG --move=$HOME/clamav/quarantine"

# Create quarantine directory
mkdir -p "$HOME/clamav/quarantine"

echo "Starting comprehensive system scan..." | tee -a "$SCAN_LOG"
echo "This may take a while depending on your system size..." | tee -a "$SCAN_LOG"
echo "" | tee -a "$SCAN_LOG"

# Scan critical system areas
echo "Scanning Home Directory..." | tee -a "$SCAN_LOG"
clamscan $SCAN_OPTIONS "$HOME" 2>&1 | tee -a "$SCAN_LOG"

echo "" | tee -a "$SCAN_LOG"
echo "Scanning Applications..." | tee -a "$SCAN_LOG"
clamscan $SCAN_OPTIONS "/Applications" 2>&1 | tee -a "$SCAN_LOG"

echo "" | tee -a "$SCAN_LOG"
echo "Scanning Downloads (if accessible)..." | tee -a "$SCAN_LOG"
if [ -d "$HOME/Downloads" ]; then
    clamscan $SCAN_OPTIONS "$HOME/Downloads" 2>&1 | tee -a "$SCAN_LOG"
fi

echo "" | tee -a "$SCAN_LOG"
echo "Scanning Desktop..." | tee -a "$SCAN_LOG"
if [ -d "$HOME/Desktop" ]; then
    clamscan $SCAN_OPTIONS "$HOME/Desktop" 2>&1 | tee -a "$SCAN_LOG"
fi

echo "" | tee -a "$SCAN_LOG"
echo "Scanning Documents..." | tee -a "$SCAN_LOG"
if [ -d "$HOME/Documents" ]; then
    clamscan $SCAN_OPTIONS "$HOME/Documents" 2>&1 | tee -a "$SCAN_LOG"
fi

echo "" | tee -a "$SCAN_LOG"
echo "=== Scan Summary ===" | tee -a "$SCAN_LOG"
echo "Scan completed at: $(date)" | tee -a "$SCAN_LOG"

# Extract infected files from log
grep "FOUND" "$SCAN_LOG" > "$INFECTED_LOG" 2>/dev/null

if [ -s "$INFECTED_LOG" ]; then
    echo "⚠️  INFECTED FILES FOUND! Check: $INFECTED_LOG" | tee -a "$SCAN_LOG"
else
    echo "✅ No infected files found!" | tee -a "$SCAN_LOG"
fi

echo "" | tee -a "$SCAN_LOG"
echo "Full scan log available at: $SCAN_LOG" | tee -a "$SCAN_LOG"
echo "Quarantined files (if any) moved to: $HOME/clamav/quarantine" | tee -a "$SCAN_LOG"
