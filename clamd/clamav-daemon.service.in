[Unit]
Description=Clam AntiVirus userspace daemon
Documentation=man:clamd(8) man:clamd.conf(5) https://docs.clamav.net/
Requires=clamav-daemon.socket
# Check for database existence
ConditionPathExistsGlob=@DATADIR@/main.{c[vl]d,inc}
ConditionPathExistsGlob=@DATADIR@/daily.{c[vl]d,inc}

[Service]
ExecStart=@prefix@/sbin/clamd --foreground=true
# Reload the database
ExecReload=/bin/kill -USR2 $MAINPID
TimeoutStartSec=420

[Install]
WantedBy=multi-user.target
Also=clamav-daemon.socket
