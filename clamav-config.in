#! /bin/sh
# Originally from libxml, Copyright (C) <PERSON>

prefix=@prefix@
exec_prefix=@exec_prefix@
includedir=@includedir@
libdir=@libdir@

usage()
{
    cat <<EOF
Usage: clamav-config [OPTION]

Known values for OPTION are:

  --prefix=DIR		change libclamav prefix [default $prefix]
  --libs		print library linking information
  --cflags		print pre-processor and compiler flags
  --help		display this help and exit
  --version		output version information
EOF

    exit $1
}

if test $# -eq 0; then
    usage 1
fi

cflags=false
libs=false

while test $# -gt 0; do
    case "$1" in
    -*=*) optarg=`echo "$1" | sed 's/[-_a-zA-Z0-9]*=//'` ;;
    *) optarg= ;;
    esac

    case "$1" in
    --prefix=*)
	prefix=$optarg
	;;

    --prefix)
	echo $prefix
	;;

    --version)
	echo @VERSION@
	exit 0
	;;

    --help)
	usage 0
	;;

    --cflags)
       	echo -I@includedir@ @CFLAGS@
       	;;

    --libs)
       	echo -L@libdir@ @LIBCLAMAV_LIBS@
       	;;

    *)
	usage
	exit 1
	;;
    esac
    shift
done

exit 0

