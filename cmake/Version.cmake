# Converts a version such as 1.2.255 to 0x0102ff
function(HexVersion version_hex_var major minor patch)
    math(EXPR version_dec "${major} * 256 * 256 + ${minor} * 256 + ${patch}")
    set(version_hex "0x")
    foreach(i RANGE 5 0 -1)
        math(EXPR num "(${version_dec} >> (4 * ${i})) & 15")
        string(SUBSTRING "0123456789abcdef" ${num} 1 num_hex)
        set(version_hex "${version_hex}${num_hex}")
    endforeach()
    set(${version_hex_var} "${version_hex}" PARENT_SCOPE)
endfunction()

# Converts a number such as 104 to 68
function(NumberToHex number output)
    set(hex "")
    foreach(i RANGE 1)
        math(EXPR nibble "${number} & 15")
        string(SUBSTRING "0123456789abcdef" "${nibble}" 1 nibble_hex)
        string(APPEND hex "${nibble_hex}")
        math(EXPR number "${number} >> 4")
    endforeach()
    string(REGEX REPLACE "(.)(.)" "\\2\\1" hex "${hex}")
    set("${output}" "${hex}" PARENT_SCOPE)
endfunction()
