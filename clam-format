#!/bin/bash

clang-format-16 -style='{ Language: Cpp, UseTab: Never, IndentWidth: 4, AlignTrailingComments: true, AlignConsecutiveAssignments: true, AlignAfterOpenBracket: true, AlignEscapedNewlines: Left, AlignOperands: true, AllowShortFunctionsOnASingleLine: Empty, AllowShortIfStatementsOnASingleLine: true, AllowShortLoopsOnASingleLine: true, BreakBeforeBraces: Linux, BreakBeforeTernaryOperators: true, ColumnLimit: 0, FixNamespaceComments: true, SortIncludes: false, MaxEmptyLinesToKeep: 1, SpaceBeforeParens: ControlStatements, IndentCaseLabels: true, DerivePointerAlignment: true }' -dump-config > .clang-format

clang-format-16 -i -verbose clamav-milter/*.c
clang-format-16 -i -verbose clamav-milter/*.h
clang-format-16 -i -verbose clambc/*.c
clang-format-16 -i -verbose clambc/*.h
clang-format-16 -i -verbose clamconf/*.c
clang-format-16 -i -verbose clamconf/*.h
clang-format-16 -i -verbose clamd/*.c
clang-format-16 -i -verbose clamd/*.h
clang-format-16 -i -verbose clamdscan/*.c
clang-format-16 -i -verbose clamdscan/*.h
clang-format-16 -i -verbose clamdtop/*.c
clang-format-16 -i -verbose clamdtop/*.h
clang-format-16 -i -verbose clamscan/*.c
clang-format-16 -i -verbose clamscan/*.h
clang-format-16 -i -verbose clamsubmit/*.c
clang-format-16 -i -verbose clamsubmit/*.h
clang-format-16 -i -verbose examples/*.c
clang-format-16 -i -verbose examples/*.h
clang-format-16 -i -verbose examples/fileprop_analysis/*.c
clang-format-16 -i -verbose examples/fileprop_analysis/old/*.c
clang-format-16 -i -verbose freshclam/*.c
clang-format-16 -i -verbose freshclam/*.h
clang-format-16 -i -verbose libclamav/*.c
clang-format-16 -i -verbose libclamav/*.h
clang-format-16 -i -verbose libclamav/jsparse/*.c
clang-format-16 -i -verbose libclamav/jsparse/*.h
clang-format-16 -i -verbose libclamav/lzw/*.c
clang-format-16 -i -verbose libclamav/lzw/*.h
clang-format-16 -i -verbose libclamav/nsis/nulsft.*
clang-format-16 -i -verbose libclamav/c++/*.cpp
clang-format-16 -i -verbose libclamav/c++/*.h
clang-format-16 -i -verbose libclamunrar_iface/*.cpp
clang-format-16 -i -verbose libclamunrar_iface/*.h
clang-format-16 -i -verbose libfreshclam/*.c
clang-format-16 -i -verbose libfreshclam/*.h
clang-format-16 -i -verbose common/*.c
clang-format-16 -i -verbose common/*.h
clang-format-16 -i -verbose sigtool/*.c
clang-format-16 -i -verbose sigtool/*.h
clang-format-16 -i -verbose clamonacc/*.c
clang-format-16 -i -verbose clamonacc/*.h
clang-format-16 -i -verbose clamonacc/*/*.c
clang-format-16 -i -verbose clamonacc/*/*.h
clang-format-16 -i -verbose unit_tests/*.c
clang-format-16 -i -verbose unit_tests/*.h
clang-format-16 -i -verbose win32/compat/*.c
clang-format-16 -i -verbose win32/compat/*.h

rustfmt `find . -name "*.rs"`

# Undo changes to specific files that we don't really want to reformat
git checkout libclamav/iana_cctld.h
git checkout libclamav/bytecode_api_decl.c
git checkout libclamav/bytecode_api_impl.h
git checkout libclamav/bytecode_hooks.h
git checkout libclamav/rijndael.c
git checkout libclamav/rijndael.h
git checkout libclamav/yara_lexer.c
git checkout libclamav/yara_grammar.c
git checkout libclamav/yara_grammar.h
git checkout libclamav/yara_exec.c
git checkout libclamav/yara_exec.h
git checkout libclamav/yara_compiler.h
git checkout libclamav/yara_compiler.h
git checkout libclamav/yara_parser.h
git checkout libclamav/yara_hash.c
git checkout libclamav/yara_hash.h
git checkout libclamav/yara_arena.c
git checkout libclamav/yara_arena.h
git checkout libclamav/inffixed64.h
git checkout libclamav/inflate64.h
git checkout libclamav/inflate64.c
git checkout libclamav/inflate64_priv.h
git checkout libclamav/queue.h
git checkout clamonacc/c-thread-pool/thpool.c
git checkout clamonacc/c-thread-pool/thpool.h
git checkout clamonacc/misc/fts.c
git checkout clamonacc/misc/priv_fts.h
