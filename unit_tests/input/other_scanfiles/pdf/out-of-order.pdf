%PDF-1.4
1 0 obj
<< /Type /Catalog
/Outlines 2 0 R
/Pages 3 0 R
>>
endobj
2 0 obj
<< /Type Outlines
/Count 0
>>
endobj
3 0 obj
<< /Type /Pages
/Kids [ 4 0 R ]
/Count 1
>>
endobj
4 0 obj
<< /Type /Page
/Parent 3 0 R
/MediaBox [ 0 0 612 792 ]
/Contents 5 0 R
/Resources << /ProcSet 6 0 R >>
>>
endobj
26 0 obj
(https://docs.clamav.net/)
endobj
24 0 obj
<< /URI (https://docs.clamav.net/manual/Development.html)
/S /URI >>
endobj
25 0 obj
<< /Type /Action /S /URI /URI 26 0 R >>
endobj
5 0 obj
<< /Length 35 >>
stream Page-marking operators endstream
endobj
6 0 obj
[ /PDF ]
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000074 00000 n
0000000119 00000 n
0000000178 00000 n
0000000299 00000 n
0000000384 00000 n
0000000440 00000 n
0000000483 00000 n
0000000555 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
408
%%EOF