##
## Example config file for freshclam
## Please read the freshclam.conf(5) manual before editing this file.
##


# Comment or remove the line below.
Example

# Path to the database directory.
# WARNING: It must match clamd.conf's directive!
# WARNING: It must already exist, be an absolute path, be writeable by
# freshclam, and be readable by clamd/clamscan.
# Default: hardcoded (depends on installation options)
#DatabaseDirectory "C:\Program Files\ClamAV\database"

# Path to the ClamAV CA certificates directory for verifying CVD signature
# archive digital signatures.
# WARNING: It must match clamd.conf's directive!
# WARNING: It must already exist, be an absolute path, be readable by
# freshclam, clamd, clamscan and sigtool.
# Default: "certs"
#CVDCertsDirectory "C:\Program Files\ClamAV\certs"

# Path to the log file (make sure it has proper permissions)
# Default: disabled
#UpdateLogFile "C:\Program Files\ClamAV\freshclam.log"

# Maximum size of the log file.
# Value of 0 disables the limit.
# You may use 'M' or 'm' for megabytes (1M = 1m = 1048576 bytes)
# and 'K' or 'k' for kilobytes (1K = 1k = 1024 bytes).
# in bytes just don't use modifiers. If LogFileMaxSize is enabled,
# log rotation (the LogRotate option) will always be enabled.
# Default: 1M
#LogFileMaxSize 2M

# Log time with each message.
# Default: no
#LogTime yes

# Enable verbose logging.
# Default: no
#LogVerbose yes

# Use system logger (can work together with UpdateLogFile).
# Default: no
#LogSyslog yes

# Specify the type of syslog messages - please refer to 'man syslog'
# for facility names.
# Default: LOG_LOCAL6
#LogFacility LOG_MAIL

# Enable log rotation. Always enabled when LogFileMaxSize is enabled.
# Default: no
#LogRotate yes

# Write the daemon's pid to the specified file.
# You must run freshclam with --daemon (-d) for freshclam to run as a daemon.
# Default: disabled
#PidFile "C:\Program Files\ClamAV\freshclam.pid"

# By default when started freshclam drops privileges and switches to the
# "clamav" user. This directive allows you to change the database owner.
# Default: clamav (may depend on installation options)
#DatabaseOwner clamav

# Use DNS to verify virus database version. FreshClam uses DNS TXT records
# to verify database and software versions. With this directive you can change
# the database verification domain.
# WARNING: Do not touch it unless you're configuring freshclam to use your
# own database verification domain.
# Default: current.cvd.clamav.net
#DNSDatabaseInfo current.cvd.clamav.net

# database.clamav.net is now the primary domain name to be used world-wide.
# Now that CloudFlare is being used as our Content Delivery Network (CDN),
# this one domain name works world-wide to direct freshclam to the closest
# geographic endpoint.
# If the old db.XY.clamav.net domains are set, freshclam will automatically
# use database.clamav.net instead.
DatabaseMirror database.clamav.net

# How many attempts to make before giving up.
# Default: 3 (per mirror)
#MaxAttempts 5

# With this option you can control scripted updates. It's highly recommended
# to keep it enabled.
# Default: yes
#ScriptedUpdates yes

# By default freshclam will keep the local databases (.cld) uncompressed to
# make their handling faster. With this option you can enable the compression;
# the change will take effect with the next database update.
# Default: no
#CompressLocalDatabase no

# With this option you can provide custom sources for database files.
# This option can be used multiple times. Support for:
#   http(s)://, ftp(s)://, or file://
# Default: no custom URLs
#DatabaseCustomURL http://myserver.example.com/mysigs.ndb
#DatabaseCustomURL https://myserver.example.com/mysigs.ndb
#DatabaseCustomURL https://myserver.example.com:4567/allow_list.wdb
#DatabaseCustomURL ftp://myserver.example.com/example.ldb
#DatabaseCustomURL ftps://myserver.example.com:4567/example.ndb
#DatabaseCustomURL file://E:\Share\local.hdb

# This option allows you to easily point freshclam to private mirrors.
# If PrivateMirror is set, freshclam does not attempt to use DNS
# to determine whether its databases are out-of-date, instead it will
# use the If-Modified-Since request or directly check the headers of the
# remote database files. For each database, freshclam first attempts
# to download the CLD file. If that fails, it tries to download the
# CVD file. This option overrides DatabaseMirror, DNSDatabaseInfo
# and ScriptedUpdates. It can be used multiple times to provide
# fall-back mirrors.
# Default: disabled
#PrivateMirror mirror1.example.com
#PrivateMirror mirror2.example.com

# Number of database checks per day.
# Default: 12 (every two hours)
#Checks 24

# Proxy settings
# The HTTPProxyServer may be prefixed with [scheme]:// to specify which kind
# of proxy is used.
#   http://     HTTP Proxy. Default when no scheme or proxy type is specified.
#   https://    HTTPS Proxy. (Added in 7.52.0 for OpenSSL, GnuTLS and NSS)
#   socks4://   SOCKS4 Proxy.
#   socks4a://  SOCKS4a Proxy. Proxy resolves URL hostname.
#   socks5://   SOCKS5 Proxy.
#   socks5h://  SOCKS5 Proxy. Proxy resolves URL hostname.
# Default: disabled
#HTTPProxyServer https://proxy.example.com
#HTTPProxyPort 1234
#HTTPProxyUsername myusername
#HTTPProxyPassword mypass

# If your servers are behind a firewall/proxy which applies User-Agent
# filtering you can use this option to force the use of a different
# User-Agent header.
# As of ClamAV 0.103.3, this setting may not be used when updating from the
# clamav.net CDN and can only be used when updating from a private mirror.
# Default: clamav/version_number (OS: ..., ARCH: ..., CPU: ..., UUID: ...)
#HTTPUserAgent SomeUserAgentIdString

# Use aaa.bbb.ccc.ddd as client address for downloading databases. Useful for
# multi-homed systems.
# Default: Use OS'es default outgoing IP address.
#LocalIPAddress aaa.bbb.ccc.ddd

# Send the RELOAD command to clamd.
# Default: no
#NotifyClamd "C:\Program Files\ClamAV\clamd.conf"

# Run command after successful database update.
# Use EXIT_1 to return 1 after successful database update.
# Default: disabled
#OnUpdateExecute command

# Run command when database update process fails.
# Default: disabled
#OnErrorExecute command

# Run command when freshclam reports outdated version.
# In the command string %v will be replaced by the new version number.
# Default: disabled
#OnOutdatedExecute command

# Don't fork into background.
# Default: no
#Foreground yes

# Enable debug messages in libclamav.
# Default: no
#Debug yes

# Timeout in seconds when connecting to database server.
# Default: 30
#ConnectTimeout 60

# Timeout in seconds when reading from database server. 0 means no timeout.
# Default: 60
#ReceiveTimeout 300

# With this option enabled, freshclam will attempt to load new databases into
# memory to make sure they are properly handled by libclamav before replacing
# the old ones.
# Tip: This feature uses a lot of RAM. If your system has limited RAM and you
# are actively running ClamD or ClamScan during the update, then you may need
# to set `TestDatabases no`.
# Default: yes
#TestDatabases no

# This option enables downloading of bytecode.cvd, which includes additional
# detection mechanisms and improvements to the ClamAV engine.
# Default: yes
#Bytecode no

# Include an optional signature databases (opt-in).
# This option can be used multiple times.
#ExtraDatabase dbname1
#ExtraDatabase dbname2

# Exclude a standard signature database (opt-out).
# This option can be used multiple times.
#ExcludeDatabase dbname1
#ExcludeDatabase dbname2
