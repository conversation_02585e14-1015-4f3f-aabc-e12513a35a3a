#include <winver.h>
#include "../libclamav/version.h"
#ifndef REPO_VERSION
#define __PLATFORM_H
#include "clamav-config.h"
#define REPO_VERSION VERSION
#endif

#define RES_VER_Q @PROJECT_VERSION_MAJOR@, @PROJECT_VERSION_MINOR@, @PROJECT_VERSION_PATCH@, 0
#define RES_VER_S "ClamAV @PROJECT_VERSION_MAJOR@.@PROJECT_VERSION_MINOR@.@PROJECT_VERSION_PATCH@-devel"

VS_VERSION_INFO VERSIONINFO
    FILEVERSION RES_VER_Q
    PRODUCTVERSION RES_VER_Q
    FILEFLAGSMASK VS_FF_DEBUG|VS_FF_PRERELEASE
#ifdef _DEBUG
    FILEFLAGS VS_FF_DEBUG|VS_FF_PRERELEASE
#else
    FILEFLAGS VS_FF_PRERELEASE
#endif
    FILEOS VOS_NT_WINDOWS32
#ifdef THIS_IS_LIBCLAMAV
    FILETYPE VFT_DLL
#else
    FILETYPE VFT_APP
#endif

    FILESUBTYPE 0
BEGIN

    BLOCK "StringFileInfo" {
        BLOCK "040904B0" {
            VALUE "CompanyName", "Cisco Systems, Inc."
            VALUE "FileDescription", RES_FDESC
            VALUE "FileVersion", REPO_VERSION
            VALUE "InternalName", RES_NAME
            VALUE "OriginalFilename", RES_FNAME
            VALUE "ProductName", "ClamAV"
            VALUE "ProductVersion", RES_VER_S " ("  REPO_VERSION ")"
            VALUE "LegalCopyright", "(C) 2025 Cisco Systems, Inc."
            VALUE "LegalTrademarks", "License: GNU GPL, Version 2"
            VALUE "Comments", REPO_VERSION
        }
    }
    BLOCK "VarFileInfo" {
	    VALUE "Translation", 0x409, 0x4b0
    }
END
