---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

<!-- STOP! Please verify that the issue you're reporting is not a security issue before you continue.

Security issues must *never* be reported on GitHub Issues because GitHub Issues are public by default. A security issue, or vulnerability, may be any bug that represents a threat to the security of the ClamAV users or any issue that a malicious person could use to cause a Denial of Service (DoS) attack on a network service running ClamAV, such as a mail filter or file upload scanner.

Read our Security Policy to find security issue reporting instructions: https://github.com/Cisco-Talos/clamav/security/policy
If you are unsure if your bug is a security issue, please report it as a security issue. -->

Describe the bug
----------------

Replace this text with a clear and concise description of the bug or feature request.

How to reproduce the problem
----------------------------

Replace this text with specific steps needed to reproduce the issue.

Replace this text with the output from the ClamAV command:
    clamconf -n

Attachments
-----------

If applicable, add screenshots to help explain your problem.

If the issue is reproducible only when scanning a specific file, attach it to the ticket.

<!-- CAUTION: Do not attach malware unless in an encrypted zip. Better yet, provide a link to the file on VirusTotal.

The maximum size for file attachments on GitHub Issues is 25MB and the maximum size for images is 10MB. If the file is too big, you can upload it to a password protected website and send us the URL and the credentials to access it.

If your file must be kept confidential you can reach out on the [ClamAV Discord chat server](https://discord.gg/6vNAqWnVgw) to exchange email addresses and to share the zipped file, or to share the zip password. -->
