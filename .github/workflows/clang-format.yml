name: clang-format

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  push:
    branches:
      - rel/*
      - dev/*
      - main
  pull_request:
    branches:
      - rel/*
      - dev/*
      - main

jobs:
  formatting-check:
    name: check
    runs-on: ubuntu-latest
    strategy:
      matrix:
        path:
          - check: "libclamav"
            exclude: "(iana_cctld|bytecode_api_|bytecode_hooks|rijndael|yara|inffixed|inflate|queue|nsis|7z|regex|c++|generated)"
          - check: "libfreshclam"
            exclude: ""
          - check: "clamav-milter"
            exclude: ""
          - check: "clambc"
            exclude: ""
          - check: "clamconf"
            exclude: ""
          - check: "clamd"
            exclude: ""
          - check: "clamdscan"
            exclude: ""
          - check: "clamdtop"
            exclude: ""
          - check: "clamonacc"
            exclude: "(c-thread-pool|fts|priv_fts)"
          - check: "clamscan"
            exclude: ""
          - check: "clamsubmit"
            exclude: ""
          - check: "freshclam"
            exclude: ""
          - check: "libfreshclam"
            exclude: ""
          - check: "common"
            exclude: ""
          - check: "sigtool"
            exclude: ""
          - check: "examples"
            exclude: ""
          - check: "win32/compat"
            exclude: ""
    steps:
      - uses: actions/checkout@v4
      - name: Run clang-format style check for C/C++ programs.
        uses: jidicula/clang-format-action@v4.13.0
        with:
          clang-format-version: "16"
          check-path: ${{ matrix.path['check'] }}
          exclude-regex: ${{ matrix.path['exclude'] }}
