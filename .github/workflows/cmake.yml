name: CMake Build

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  push:
    branches:
      - rel/*
      - dev/*
      - main
  pull_request:
    branches:
      - rel/*
      - dev/*
      - feature/*
      - main

env:
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release
  VCPKG_GIT_REF: 8a9a97315aefb3f8bc5d81bf66ca0025938b9c91

jobs:
  build-windows:
    runs-on: windows-2019

    steps:
      - name: Die, link!!!!!
        run: rm /usr/bin/link.exe
        shell: bash

      - uses: actions/checkout@v4

      - name: Install Build Tools
        uses: crazy-max/ghaction-chocolatey@v3
        with:
          args: install wixtoolset

      - name: Install pytest for easier to read test results
        run: python3 -m pip install pytest

      - uses: lukka/get-cmake@v3.30.0

      # Restore from cache the previously built ports. If cache-miss, download, build vcpkg ports.
      - name: Restore vcpkg ports from cache or install vcpkg
        # Download and build vcpkg, without installing any port. If content is cached already, it is a no-op.
        uses: lukka/run-vcpkg@v7.4
        id: runvcpkg
        with:
          vcpkgArguments: "curl[openssl] openssl:x64-windows-static-md json-c libxml2 pcre2 check pthreads zlib pdcurses bzip2"
          vcpkgGitCommitId: "${{ env.VCPKG_GIT_REF }}"
          vcpkgTriplet: "x64-windows"

      - name: Print the VCPKG_ROOT & VCPKG_TRIPLET (for debugging)
        shell: bash
        run: echo "'${{ steps.runvcpkg.outputs.RUNVCPKG_VCPKG_ROOT_OUT }}' '${{ steps.runvcpkg.outputs.RUNVCPKG_VCPKG_TRIPLET_OUT }}' "

      - name: dir the VCPKG_ROOT
        run: dir ${{ steps.runvcpkg.outputs.RUNVCPKG_VCPKG_ROOT_OUT }}

      - name: Create Build Directory
        shell: bash
        # Some projects don't allow in-source building, so create a separate build directory
        # We'll use this as our working directory for all subsequent commands
        run: cmake -E make_directory ${{runner.workspace}}/build

      - name: Run CMake+Ninja with triplet (cmd)
        uses: lukka/run-cmake@v3.4
        id: runcmake_cmd
        with:
          cmakeGenerator: "Ninja" # Visual Studio 15 2017
          cmakeListsOrSettingsJson: "CMakeListsTxtBasic"
          cmakeListsTxtPath: "${{runner.workspace}}/clamav/CMakeLists.txt"
          useVcpkgToolchainFile: true
          cmakeAppendedArgs: '-A x64 -DCMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake" -DENABLE_EXAMPLES=ON -DENABLE_STATIC_LIB=ON -- -v'
          cmakeBuildType: "${{ env.BUILD_TYPE }}"
          vcpkgTriplet: ${{ steps.runvcpkg.outputs.RUNVCPKG_VCPKG_TRIPLET_OUT }}
          buildDirectory: "${{runner.workspace}}/build"

      - name: Test
        working-directory: ${{runner.workspace}}/build
        # Execute tests defined by the CMake configuration.
        # See https://cmake.org/cmake/help/latest/manual/ctest.1.html for more detail
        run: ctest -C ${{ env.BUILD_TYPE }} -V

      - name: Create Installer
        working-directory: ${{runner.workspace}}/build
        run: cpack -C ${{ env.BUILD_TYPE }}

  build-macos:
    runs-on: macos-latest

    steps:
      - uses: actions/checkout@v4

      - name: Install Build Tools
        run: brew install bison flex pipx

      - name: Install Dependencies
        run: brew install bzip2 check curl json-c libxml2 ncurses openssl@3 pcre2 zlib

      - name: Install pytest for easier to read test results
        run: pipx install pytest

      - uses: lukka/get-cmake@v3.30.0

      - name: Create Build Directory
        shell: bash
        # Some projects don't allow in-source building, so create a separate build directory
        # We'll use this as our working directory for all subsequent commands
        run: cmake -E make_directory ${{runner.workspace}}/build

      - name: Configure CMake
        # Use a bash shell so we can use the same syntax for environment variable
        # access regardless of the host operating system
        working-directory: ${{runner.workspace}}/build
        # Note the current convention is to use the -S and -B options here to specify source
        # and build directories, but this is only available with CMake 3.13 and higher.
        # The CMake binaries on the Github Actions machines are (as of this writing) 3.12
        run:
          cmake ${{runner.workspace}}/clamav -DCMAKE_BUILD_TYPE=${{ env.BUILD_TYPE }}
          -DOPENSSL_ROOT_DIR=/opt/homebrew/include/
          -DOPENSSL_CRYPTO_LIBRARY=/opt/homebrew/lib/libcrypto.3.dylib
          -DOPENSSL_SSL_LIBRARY=/opt/homebrew/lib/libssl.3.dylib
          -DENABLE_STATIC_LIB=ON
          -DENABLE_EXAMPLES=ON

      - name: Build
        shell: bash
        working-directory: ${{runner.workspace}}/build
        # Execute the build.  You can specify a specific target with "--target <NAME>"
        run: cmake --build . --config ${{ env.BUILD_TYPE }}

      - name: Test
        shell: bash
        working-directory: ${{runner.workspace}}/build
        # Execute tests defined by the CMake configuration.
        # See https://cmake.org/cmake/help/latest/manual/ctest.1.html for more detail
        run: ctest -C ${{ env.BUILD_TYPE }} -V

  build-ubuntu:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Update package listings
        run: sudo apt-get update

      - name: Install Build Tools
        run: sudo apt-get install -y bison flex valgrind pipx

      - name: Install Dependencies
        run: sudo apt-get install -y check libbz2-dev libcurl4-openssl-dev libjson-c-dev libmilter-dev libncurses5-dev libpcre3-dev libssl-dev libxml2-dev zlib1g-dev

      - name: Install pytest for easier to read test results
        run: pipx install pytest

      - uses: lukka/get-cmake@v3.30.0

      - name: Create Build Directory
        shell: bash
        # Some projects don't allow in-source building, so create a separate build directory
        # We'll use this as our working directory for all subsequent commands
        run: cmake -E make_directory ${{runner.workspace}}/build

      - name: Configure CMake
        # Use a bash shell so we can use the same syntax for environment variable
        # access regardless of the host operating system
        working-directory: ${{runner.workspace}}/build
        # Note the current convention is to use the -S and -B options here to specify source
        # and build directories, but this is only available with CMake 3.13 and higher.
        # The CMake binaries on the Github Actions machines are (as of this writing) 3.12
        run:
          cmake ${{runner.workspace}}/clamav -DCMAKE_BUILD_TYPE=${{ env.BUILD_TYPE }}
          -DENABLE_STATIC_LIB=ON
          -DENABLE_EXAMPLES=ON

      - name: Build
        shell: bash
        working-directory: ${{runner.workspace}}/build
        # Execute the build.  You can specify a specific target with "--target <NAME>"
        run: cmake --build . --config ${{ env.BUILD_TYPE }}

      - name: Test
        shell: bash
        working-directory: ${{runner.workspace}}/build
        # Execute tests defined by the CMake configuration.
        # See https://cmake.org/cmake/help/latest/manual/ctest.1.html for more detail
        run: ctest -C ${{ env.BUILD_TYPE }} -V
